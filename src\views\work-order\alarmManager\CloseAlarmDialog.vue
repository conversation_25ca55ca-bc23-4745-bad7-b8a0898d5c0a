<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="预警描述" prop="workOrderDesc">
       预警消息：哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈
      </el-form-item>
      <el-form-item
        label="附件"
        prop="fileList"
        class="fileList"
      >
        <multi-upload v-model="fileList" :limit="fileList.length" :showDelete="false"></multi-upload>
      </el-form-item>
      <el-form-item label="预警处置人" prop="processor">张三</el-form-item>
      
      <el-form-item  label="处置原因" prop="disposalReason">
        <el-radio-group v-model="form.disposalReason" @change="handleReasonChange">
          <el-radio label="误报">误报</el-radio>
          <el-radio label="测试">测试</el-radio>
          <el-radio label="重复预警"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item  label="处置说明" prop="disposalDesc">
       <el-input
          v-model="form.disposalDesc"
          placeholder="请输入处置说明"
          maxlength="250"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { listUser } from '@/api/system/user';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({
  disposalReason: null,
  disposalDesc: '',
});
const dataFormRef = ref(ElForm);
const userOptinos = ref([]);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true,immediate:true }
);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  processor: [{ required: true, message: '请选择处置人', trigger: ['change','blur'] }],
  disposalDesc: [{ required: true, message: '请输入处置说明', trigger: ['change','blur']}],
});

const fileList = [
  {
    name: 'test.mp4',
    url: 'https://testing.ff-iot.com:24135/ff_user/Know/2025/06/09/73FC67D9E38B4739AA2236474BA430CC.mp4',
  },
  {
    name: 'element-plus-logo2.svg',
    url: 'http://**************:24176/uavfile/wayline/thumbnai/2025/04/wayline/2025/04/org_0f7089d44ad4e389_1744250928000.jpg_1744251780410_thumb.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=uav%2F20250723%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250723T081436Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=36f01afe566f8c09498287d010fa7ca83ef2fb0f06c906448017fd14b8d034ef',
  },
]

defineExpose({ setDefaultValue });

// 设置默认值
function setDefaultValue() {
  
}
const loading = ref(false);

//处置原因
function handleReasonChange(val){
  form.disposalReason = val
  form.disposalDesc = `${val}${form.disposalDesc}`
}
// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
    console.log('3=====',form)
    //   let params = {
    //     ...form,
      
    //   };
    //   loading.value = true;
    //   addAirTaskList(params)
    //     .then(res => {
    //       loading.value = false;
    //       ElMessage.success('新增成功');
    //       closeDialog();
    //       emit('submit');
    //     })
    //     .catch(e => {
    //       loading.value = false;
    //     });
    } else {
      loading.value = false;
    }
  });
}
//处置人
function getUser() {
  listUser({
    pageNo: 1,
    pageSize: 999
  }).then(res => {
    const { list = [] } = res;
    userOptinos.value = list;
  });
}

onMounted(() => {
  getUser();
});
</script>
<style scoped lang="scss">

:global(.el-loading-mask) {
  transform: opacity 0.9 !important;
  background-color: rgb(255 255 255 / 0.3);
}
.app-form {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
</style>
