<!--工单管理-->
<script>
export default {
  name: 'orderManager'
};
</script>

<script setup>
import { nextTick, onBeforeUnmount, reactive } from 'vue';
import DetailDialog from './DetailDialog.vue';
import EditDialog from './EditDialog.vue';
import DisposalDialog from './DisposalDialog.vue';
import EndOrderDialog from './EndOrderDialog.vue';
import optionData from '@/utils/option-data';
import { getAirTaskList, deleteAirTaskList, pauseTask } from '@/api/task';
import { COMMON_COLOR, TASK_TYPE, DOMAIN } from '@/utils/constants';
import { getDevicesBound } from '@/api/devices';
import { ElMessage } from 'element-plus';
import moment from 'moment';
import { authorityShow } from '@/utils/authority';
import { listUser } from '@/api/system/user';
const editDialogRef = ref(null);
const exeAirportOptions = ref([]);

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  job_types: [0, 1],
  page_num: 1,
  page_size: 10
});

const dataList = ref([]);
const dialog = reactive({
  visible: false
});

const editDialog = reactive({
  visible: false
});
const disposalDialog = reactive({
  visible: false,
  title:'工单处置'
});
const endOrderDialog = reactive({
  visible: false,
  title:'结束工单'
});
let formData = reactive({});
let detailData = reactive({});
const userOptinos = ref([]);

function getDevices() {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page_num: 1,
    page_size: 50
  }).then(res => {
    const { list = [] } = res;
    exeAirportOptions.value = list;
  });
}

/**
 * 查询
 */
function handleQuery(params) {
  loading.value = true;
  getAirTaskList({
    ...queryParams,
    ...params,
  }).then(data => {
    const { records } = data;
    dataList.value = records || [];
    loading.value = false;
    queryParams.page_num = data.current;
    total.value = data.total;
  });
}

function handleSearchJobType (value) {
  const params = {
    ...queryParams,
    job_types: value!==null && value !== '' ? [value] : [0,1],
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: 1
  };
  delete params.rangTime;
  handleQuery(params);
}

function handleSearch() {
  console.log('queryParams',queryParams)
  const params = {
    ...queryParams,
    job_types: queryParams.hasOwnProperty('job_type') && queryParams.job_type !== null && queryParams.job_type !=='' ? [queryParams.job_type] : [0,1],
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: 1
  };
  delete params.rangTime;
  handleQuery(params);
}
function handleSearchNowPage() {
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: queryParams.page_num,
    job_types: [0, 1]
  };
  delete params.rangTime;
  handleQuery(params);
}

/**
 * 重置查询
 */
function resetQuery() {
  queryParams.name = '';
  queryParams.wayline_file_name = '';
  queryParams.dock_sn = '';
  queryParams.begin_time = '';
  queryParams.end_time = '';
  queryParams.status = '';
  queryParams.task_type = '';
  queryParams.job_type = '';
  queryParams.job_types = [0, 1];
  queryParams.rangTime = '';
  queryParams.page_num = 1;
  queryParams.page_size = 10;
  handleQuery({ ...queryParams });
}



function openDetail() {
  dialog.visible = true;
}

//新建工单
function addWorkOrder() {
  editDialog.title = '新建工单';
  editDialog.visible = true;
}
//工单处置
function workDisposal(row){
  disposalDialog.visible = true;
}
//结束工单
function endOrder(row){
  endOrderDialog.visible = true;
}
//处置人
function getUser() {
  listUser({
    pageNo: 1,
    pageSize: 999
  }).then(res => {
    const { list = [] } = res;
    userOptinos.value = list;
  });
}

onMounted(() => {
  window.addEventListener('setItem', () => {
    handleQuery();
  });
  getUser();
  getDevices();
  handleQuery({ ...queryParams });
});

const handleSizeChange = val => {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / val);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page_num > newTotalPages) {
    queryParams.page_num = newTotalPages || 1;
  }
  
  queryParams.page_size = val;
  const params = {
    ...queryParams,
    job_types: queryParams.hasOwnProperty('job_type') && queryParams.job_type !== null && queryParams.job_type !== '' ? [queryParams.job_type] : [0, 1],
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_size: val,
    page_num: queryParams.page_num // Use the potentially adjusted page number
  };
  handleQuery({ ...params });
};

const handleCurrentChange = val => {
  queryParams.page_num = val;
  const params = {
    ...queryParams,
    job_types:  queryParams.hasOwnProperty('job_type') && queryParams.job_type != null && queryParams.job_type !=='' ? [queryParams.job_type] : [0,1],
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: val
  };
  handleQuery({ ...params });
};

onBeforeUnmount(() => {
  window.removeEventListener('setItem', () => {});
});

/**
 * 删除工单
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此工单，且无法进行恢复`, '确认删除所选工单？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    // delUser(row.id).then(data => {
    //   ElMessage.success('删除成功');
    //   handleQuery();
    // });
  });
}
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="keyWord">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.rangTime"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleSearch"
            />
          </el-form-item>
          <el-form-item label="" prop="status">
            <el-select
              class="input-serach"
              v-model="queryParams.status"
              placeholder="请选择工单类型"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData.airStatusList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="task_type">
            <el-select
              class="input-serach"
              v-model="queryParams.task_type"
              placeholder="请选择工单来源"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData.typeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="job_type">
            <el-select
              class="input-serach"
              v-model="queryParams.job_type"
              placeholder="请选择工单状态"
              clearable
              @change='(value)=>{handleSearchJobType(value)}'
            >
              <el-option
                v-for="(item, index) in optionData.jobOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="job_type">
            <el-select
              class="input-serach"
              v-model="queryParams.job_type"
              placeholder="请选择优先级"
              clearable
              @change='(value)=>{handleSearchJobType(value)}'
            >
              <el-option
                v-for="(item, index) in optionData.jobOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="job_type">
            <el-select
              class="input-serach"
              v-model="queryParams.job_type"
              placeholder="请选择处置人"
              clearable
            >
              <el-option
                v-for="(item, index) in userOptinos"
                :key="item.user_id"
                :label="item.username"
                :value="item.user_id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="wayline_file_name">
            <el-input
              class="input-serach"
              v-model="queryParams.wayline_file_name"
              placeholder="请输入预警描述"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn" style="transform: translateY(15px)">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>

    <el-card shadow="never">
      <template #header>
        <el-button type="primary" @click="addWorkOrder()" v-if="authorityShow('createAirportTask')"><i-ep-plus />新建工单</el-button>
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540" >
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.page_size * (queryParams.page_num - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="产生时间" prop="flight_task_id" width="200" show-overflow-tooltip />
        <el-table-column label="工单类型" prop="name" show-overflow-tooltip />
        <el-table-column label="工单来源" prop="wayline_file_name" show-overflow-tooltip />
        <el-table-column label="工单主题" prop="execute_time"  show-overflow-tooltip />
        <el-table-column label="工单状态" show-overflow-tooltip prop="task_type_desc" />
        <!-- <el-table-column label="任务类型" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ optionData.jobOptions.find(item => item.value === scope.row.job_type)?.label }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="优先级" prop="create_user_name" show-overflow-tooltip></el-table-column>
        <el-table-column label="工单处置人" prop="create_user_name" show-overflow-tooltip></el-table-column>
        <el-table-column label="处置说明" prop="dock_sn_desc" show-overflow-tooltip> </el-table-column>
        <el-table-column label="最后处置时间" prop="dock_sn_desc" show-overflow-tooltip> </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="200" v-if="authorityShow('controlAirportTask') || authorityShow('checkAirportTask') || authorityShow('deleteAirportTask')">
          <template #default="scope">
            <el-button
              type="danger"
              v-if= "authorityShow('controlAirportTask')" 
              link
              @click.stop="workDisposal(scope.row)"
              >处置</el-button>
            <el-button
              type="danger"
              v-if= "authorityShow('controlAirportTask')" 
              link
              @click.stop="endOrder(scope.row)"
              >结束</el-button>
            <el-button type="primary" 
            link @click.stop="openDetail(scope.row)" 
            v-if="authorityShow('checkAirportTask')">详情</el-button>
            <el-button
              type="danger"
              v-if="authorityShow('controlAirportTask')"
              link
              @click.stop="handleDelete(scope.row)"
              >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-content">
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.page_num"
          v-model:page-size="queryParams.page_size"
          :background="true"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <div v-if="editDialog.visible">
      <EditDialog
        v-if="editDialog.visible"
        ref="editDialogRef"
        v-model:visible="editDialog.visible"
        :title="editDialog.title"
        :form-data="formData"
        @submit="resetQuery"
      />
    </div>

    <div v-if="disposalDialog.visible">
      <DisposalDialog
        v-if="disposalDialog.visible"
        v-model:visible="disposalDialog.visible"
        :title="disposalDialog.title"
        :form-data="formData"
        @submit="resetQuery"
      />
    </div>

    <div v-if="endOrderDialog.visible">
      <EndOrderDialog
        v-if="endOrderDialog.visible"
        v-model:visible="endOrderDialog.visible"
        :title="endOrderDialog.title"
        :form-data="formData"
        @submit="resetQuery"
      />
    </div>

    <div v-if="dialog.visible">
      <DetailDialog v-model:visible="dialog.visible" :form-data="detailData" />
    </div>
  </div>
</template>
<style scoped lang="scss">
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  .search-form {
    flex: 1;
  }
}
</style>
