<template>
  <el-dialog :title="title" 
  v-if="visible" 
  :model-value="visible" 
  align-center 
  :close-on-click-modal="false" 
  width="600px"
  @close="closeDialog">
      <el-form class="app-form" ref="dataFormRef"  label-width="120px"  v-loading="loading">
        <el-form-item label="预警描述：" >
          {{form?.alarm_desc || '-'}}
        </el-form-item>
        <el-form-item label="附件：" >
          <multi-upload v-model="fileList" :limit="fileList.length" :showDelete="false" v-if="fileList.length!=0"></multi-upload>
          <span v-else>-</span>
        </el-form-item>
        <el-form-item label="预警处置人：" >
          {{form?.processor || '-'}}
        </el-form-item>
        <el-form-item label="处置说明：" >
          {{form?.process_content || '-'}}
        </el-form-item>
      </el-form>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { getAlarmDetail } from '@/api/workOrder';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '详情'
  },
  id: {
    type: String,
    default: ''
  },
});
const form = ref({});
const loading = ref(false);
const emit = defineEmits(['update:visible']);
const fileList = ref([])

// 关闭弹窗
function closeDialog() {
  fileList.value = [];
  emit('update:visible', false);
}

// 获取详情
function getDetail() {
  loading.value = true
  getAlarmDetail(props.id).then(res => {
    form.value = res
    if(res.attachments && res.attachments.length !=0){
      let fileAry = []
      res.attachments.forEach(item=>{
        fileAry.push({
          name:item.file_name,
          url:item.access_url
        })
      })
      fileList.value = fileAry
    }
  }).finally(()=>{
    loading.value = false
  })
}

onMounted(() => {
  getDetail();
});
</script>
<style lang="scss">
.label-class {
  display: inline-block;
  width: 110px;
  text-align: right;
}
</style>
<style lang="scss" scoped>

</style>
