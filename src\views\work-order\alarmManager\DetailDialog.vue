<template>
  <el-dialog :title="title" 
  v-if="visible" 
  :model-value="visible" 
  align-center 
  :close-on-click-modal="false" 
  width="600px"
  @close="closeDialog">
       <el-form class="app-form" ref="dataFormRef"  label-width="120px"  v-loading="loading">
        <el-form-item label="预警描述：" >
          哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈
        </el-form-item>
        <el-form-item label="附件：" >
          <multi-upload v-model="fileList" :limit="fileList.length" :showDelete="false"></multi-upload>
        </el-form-item>
        <el-form-item label="预警处置人：" >
          张三
        </el-form-item>
        <el-form-item label="处置说明：" >
          哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈
        </el-form-item>
      </el-form>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getAIEventDetail } from '@/api/workOrder';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '详情'
  },
  id: {
    type: String,
    default: ''
  },
});
const form = ref({});
const loading = ref(false);
const emit = defineEmits(['update:visible']);
const fileList = [
  {
    name: 'test.mp4',
    url: 'https://testing.ff-iot.com:24135/ff_user/Know/2025/06/09/73FC67D9E38B4739AA2236474BA430CC.mp4',
  },
  {
    name: 'element-plus-logo2.svg',
    url: 'http://**************:24176/uavfile/wayline/thumbnai/2025/04/wayline/2025/04/org_0f7089d44ad4e389_1744250928000.jpg_1744251780410_thumb.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=uav%2F20250723%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250723T081436Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=36f01afe566f8c09498287d010fa7ca83ef2fb0f06c906448017fd14b8d034ef',
  },
]

// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}

// 获取详情
function getDetail() {
  loading.value = true
  getAIEventDetail(props.id).then(res => {
    form.value = res
  }).finally(()=>{
    loading.value = false
  })
}

onMounted(() => {
  // getDetail();
});
</script>
<style lang="scss">
.label-class {
  display: inline-block;
  width: 110px;
  text-align: right;
}
</style>
<style lang="scss" scoped>

</style>
