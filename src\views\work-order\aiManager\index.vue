<!--AI事件管理-->
<script>
export default {
  name: 'aiManager'
};
</script>

<script setup>
import { nextTick, reactive } from 'vue';
import DetailDialog from './DetailDialog.vue';
import { getAIEventList } from '@/api/workOrder';

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
});

const dataList = ref([]);
const dialog = reactive({
  visible: false,
  id:''
});

/**
 * 查询
 */
function handleQuery(params) {
  loading.value = true;
  getAIEventList({
    ...queryParams,
    ...params,
  }).then(data => {
    const { list } = data;
    dataList.value = list || [];
    loading.value = false;
    total.value = data.total;
  });
}

/**
 *
 * 查看
 */
function openDialog(row) {
  dialog.id = row.id;
  dialog.visible = true;
}


onMounted(() => {
  handleQuery({ ...queryParams });
});

const handleSizeChange = val => {
  const newTotalPages = Math.ceil(total.value / val);
  if (queryParams.pageNo > newTotalPages) {
    queryParams.pageNo = newTotalPages || 1;
  }
  queryParams.pageSize = val;
  const params = {
    pageSize: val,
    pageNo: queryParams.pageNo // Use the potentially adjusted page number
  };
  handleQuery({ ...params });
};

const handleCurrentChange = val => {
  queryParams.pageNo = val;
  const params = {
    ...queryParams,
    pageNo: val
  };
  handleQuery({ ...params });
};

</script>

<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="560" style="padding-top: 20px;">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNo - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="设备名称" prop="device_name" width="200" show-overflow-tooltip />
        <el-table-column label="任务名称" prop="task_name" show-overflow-tooltip />
        <el-table-column label="算法应用名称" prop="app_name" show-overflow-tooltip />
        <el-table-column label="摄像头名称" prop="src_name"  show-overflow-tooltip />
        <el-table-column label="创建时间" prop="create_time" show-overflow-tooltip />
        <el-table-column label="是否已生成预警" prop="has_alarm" show-overflow-tooltip>
          <template #default="scope">
            <span>{{scope.row?.has_alarm == false?'未生辰':'已生成'}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="100" >
          <template #default="scope">
            <el-button type="primary" 
            link @click.stop="openDialog(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-content">
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :background="true"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <div v-if="dialog.visible">
      <DetailDialog v-model:visible="dialog.visible" :id="dialog.id" />
    </div>
  </div>
</template>
<style scoped lang="scss">
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  // align-items: center;
  padding: 24px 24px 8px;
  .search-form {
    flex: 1;
  }
}
</style>
