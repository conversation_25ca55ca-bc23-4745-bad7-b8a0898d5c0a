<script>
export default { name: 'mapTools' };
</script>

<script setup>
import { ref, reactive, onMounted, toRaw } from 'vue';
import {
  getOrCreateCesiumEngineInstance,
  getCesiumEngineInstance,
  delCesiumEngineInstance,
  flyTo,
  imglayer,
  veclayer,
  cialayer,
  cvalayer,
  toDegrees,
  drawLineMeasureGraphics,
  drawAreaMeasureGraphics,
  drawElementGather,
  toCartesian3,
  getCenterCoordinates,
  arrayToCartesian3
} from '@/components/Cesium/libs/cesium';
import * as Cesium from 'cesium';
import { getJJTaskPage } from '@/api/task';
import { getDevicesBound } from '@/api/devices';
import { DOMAIN } from '@/utils/constants';
import * as turf from '@turf/turf';
import { getFlightDataList } from '@/views/flight-manage/flight-area/flightAreaAxiosHandle';
import { FLIGHTAREATYPE } from '@/views/flight-manage/flight-area/flightAreaHandle';
import { GEOMTYPE } from '@/views/flight-manage/flight-area/flightAreaHandle';
import { getFenceList } from '@/api/fence';
import { getAllLayers } from '@/api/map/layer';
import { getFeaturesByLayer } from '@/api/map/feature';

// 灾害点图标
let firePnt_url = new URL('@/assets/gis/firePnt.png', import.meta.url).href;

const props = defineProps({
  // 是否显示标绘图层
  showPoltLyr: {
    type: Boolean,
    default: false
  }
});
// 图层控制
const isLayerActive = ref(false);
// 底图控制
const isEarthActive = ref(false);
// 鹰眼控制
const isEyeActive = ref(false);
// 空间搜索
const isSearchPolygonActive = ref(false);
// 搜索
const isSearchActive = ref(false);
const searchType = ref(0);
const searchName = ref('');
const searchTypeOptions = [
  {
    value: 0,
    label: '机场'
  },
  {
    value: 1,
    label: '无人机'
  },
  {
    value: 2,
    label: '警情'
  }
];
// 搜索结果
const searchResult = ref([]);
const searchPolygonResult = ref([]);
// 机场列表
const dockList = ref([]);
// 无人机列表
const navList = ref([]);
// 图层控制显影
const lyr_img = ref(true);
const lyr_vec = ref(false);
const lyr_noFly = ref(false);
const lyr_limitHeight = ref(false);
const lyr_airPortArea = ref(false);
const lyr_limitFlightArea = ref(false);
const lyr_workFlightArea = ref(false);
const lyr_polt = ref(false);
const lyr_fireCar = ref(true);
// 新增图层控制
const lyr_fence = ref(false);
const dynamicLayers = ref([]);

// 围栏数据源
const fenceLayer = new Cesium.CustomDataSource('fenceLayer');
// 动态图层数据源
const dynamicLayerSources = new Map();

// 空间查询图层
const drawElementLayer = new Cesium.CustomDataSource('drawElementLayer');
const theDrawElementGather = drawElementGather(drawElementLayer);
// 自定义限飞区数据源
const limitFlightLyr = new Cesium.CustomDataSource('limitFlightLyr');
// 自定义限飞区数据源
const workFlightLyr = new Cesium.CustomDataSource('workFlightLyr');

// 空间查询要素的颜色
const drawElementColor = '#3b6cca';
// 查询结果集
const searchResultLayer = new Cesium.CustomDataSource('searchResultLayer');
// 测量工具handler
let measureLineHandler = null;

// 图标列表
const tools = reactive([
  { name: 'layerControl', icon: 'layers', cn: '图层控制' },
  { name: 'mapControl', icon: 'map', cn: '地图切换' },
  { name: 'eyeControl', icon: 'eye', cn: '鹰眼' },
  { name: 'searchControl', icon: 'search', cn: '搜索' },
  { name: 'airportAreaControl', icon: 'airport_area', cn: '机场覆盖范围' },
  { name: 'pntControl', icon: 'pnt', cn: '点选查询' },
  { name: 'lineControl', icon: 'line', cn: '线选查询' },
  { name: 'squareControl', icon: 'square', cn: '框选查询' },
  { name: 'circleControl', icon: 'circle', cn: '圈选查询' },
  { name: 'polygonControl', icon: 'polygon', cn: '多边形查询' },
  { name: 'measureLineControl', icon: 'measure_line', cn: '测量距离' },
  { name: 'measurePolygonControl', icon: 'measure_polygon', cn: '测量面积' },
  { name: 'deleteControl', icon: 'delete', cn: '清空' }
]);

// 工具选中
const toolClicks = reactive({
  layerControl: false,
  mapControl: false,
  eyeControl: false,
  searchControl: false,
  airportAreaControl: false,
  pntControl: false,
  lineControl: false,
  squareControl: false,
  circleControl: false,
  polygonControl: false,
  measureLineControl: false,
  measurePolygonControl: false,
  deleteControl: false
});

// 根据工具名称调用对应的控制方法
const selectTool = toolName => {
  const method = methodsMap[toolName];
  if (method) {
    destroyMeasureLineHandler();
    method();
    setTimeout(() => {}, 200);
  }
};
// 图层控制
const control_lyr = () => {
  controlMapLayer('layer');
};
// 地图切换
const control_map = () => {
  controlMapLayer('earth');
};
// 鹰眼
const control_eye = () => {
  isEyeActive.value = !isEyeActive.value;
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  if (isEyeActive.value) {
    setTimeout(() => {
      let eyeEngine = getOrCreateCesiumEngineInstance('eyeView-fly');
      eyeEngine?.init('map-eye-content');
      let eyeView = eyeEngine.viewer;
      // 天地图影像、矢量、影像注记、矢量注记
      const imageryLayers = eyeView.imageryLayers;
      imageryLayers.addImageryProvider(imglayer, 0);
      imageryLayers.addImageryProvider(cialayer, 1);
      imageryLayers.addImageryProvider(veclayer, 2);
      imageryLayers.addImageryProvider(cvalayer, 3);
      // 修改影像图层的gamma值
      imageryLayers.get(0).gamma = 0.75;
      imageryLayers.get(1).gamma = 0.75;
      imageryLayers.get(2).show = lyr_vec.value;
      imageryLayers.get(3).show = lyr_vec.value;

      let control = eyeView.scene.screenSpaceCameraController;
      control.enableRotate = false;
      control.enableTranslate = false;
      control.enableZoom = false;
      control.enableTilt = false;
      control.enableLook = false;
      let syncViewer = function () {
        // 获取当前相机的位置
        let currentPosition = toDegrees(flyView.camera.position);
        // 放大视图范围：增加z轴值
        let newDestination = Cesium.Cartesian3.fromDegrees(
          currentPosition[0],
          currentPosition[1],
          currentPosition[2] + 800
        );
        eyeView.camera.flyTo({
          // destination: flyView.camera.position,
          destination: newDestination,
          orientation: {
            heading: flyView.camera.heading,
            pitch: flyView.camera.pitch,
            roll: flyView.camera.roll
          },
          duration: 0.0
        });
      };
      flyView.entities.add({
        id: 'syncEyeViewer',
        position: Cesium.Cartesian3.fromDegrees(0, 0),
        label: {
          text: new Cesium.CallbackProperty(function () {
            syncViewer();
            return '';
          }, true)
        }
      });
    }, 200);
  } else if (!isEyeActive.value) {
    flyView.entities.removeById('syncEyeViewer');
    delCesiumEngineInstance('eyeView-fly');
  }
};

// 地图搜索
const control_search = () => {
  console.log('manager_search');
  isSearchActive.value = !isSearchActive.value;
};

// 机场覆盖区域
const control_airportArea = () => {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const airPortAreaLyr = flyView.dataSources.getByName('airPortAreaLyr')[0];
  lyr_airPortArea.value = !lyr_airPortArea.value;
  airPortAreaLyr.show = lyr_airPortArea.value;
};
// 点选查询
const control_pnt = () => {
  const gatherPointCallback = gatherPoint => {
    console.log('坐标采集成功,其对象为：', gatherPoint);
    console.log('坐标采集成功,其坐标为：', gatherPoint.centerPosition);
    let feature = createTurfolygon('pnt', gatherPoint);
    if (feature !== null) {
      searchDevicesInPolygon(feature);
    }
  };
  theDrawElementGather.gatherHandlerDestroy();
  theDrawElementGather.pointGather(gatherPointCallback, {
    color: drawElementColor,
    alpha: 0.5,
    viewer: getCesiumEngineInstance('homeMap-fly').viewer,
    elementId: 'mainMapContainer'
  });
};
// 线选查询
const control_line = () => {
  const gatherPolylineCallback = gatherPolyline => {
    console.log('坐标采集成功,其对象为：', gatherPolyline);
    console.log('坐标采集成功,其坐标为：', gatherPolyline.gatherPoints);
    let feature = createTurfolygon('line', gatherPolyline);
    if (feature !== null) {
      searchDevicesInPolygon(feature);
    }
  };
  theDrawElementGather.gatherHandlerDestroy();
  theDrawElementGather.polylineGather(gatherPolylineCallback, {
    color: drawElementColor,
    alpha: 0.9,
    viewer: getCesiumEngineInstance('homeMap-fly').viewer,
    elementId: 'mainMapContainer'
  });
};
// 矩形框选查询
const control_square = () => {
  const rectangleGatherPolygonCallback = gatherRectangle => {
    console.log('坐标采集成功,其对象为：', gatherRectangle);
    console.log('坐标采集成功,其坐标为：', gatherRectangle.gatherPoints);
    let feature = createTurfolygon('square', gatherRectangle);
    if (feature !== null) {
      searchDevicesInPolygon(feature);
    }
  };
  theDrawElementGather.gatherHandlerDestroy();
  theDrawElementGather.rectangleGather(rectangleGatherPolygonCallback, {
    color: drawElementColor,
    alpha: 0.5,
    viewer: getCesiumEngineInstance('homeMap-fly').viewer,
    elementId: 'mainMapContainer'
  });
};
// 圈选查询
const control_circle = () => {
  const gatherCircleCallback = gatherCircle => {
    console.log('坐标采集成功,其对象为：', gatherCircle);
    console.log('坐标采集成功,其坐标为：', gatherCircle.centerPosition);
    console.log('坐标采集成功,其坐标为：', gatherCircle.radius);
    let feature = createTurfolygon('circle', gatherCircle);
    if (feature !== null) {
      searchDevicesInPolygon(feature);
    }
  };
  theDrawElementGather.gatherHandlerDestroy();
  theDrawElementGather.circleGather(gatherCircleCallback, {
    color: drawElementColor,
    alpha: 0.5,
    viewer: getCesiumEngineInstance('homeMap-fly').viewer,
    elementId: 'mainMapContainer'
  });
};
// 多边形选查询
const control_polygon = () => {
  const gatherPolygonCallback = gatherPolygon => {
    console.log('坐标采集成功,其对象为：', gatherPolygon);
    console.log('坐标采集成功,其坐标为：', gatherPolygon.gatherPoints);
    let feature = createTurfolygon('polygon', gatherPolygon);
    if (feature !== null) {
      searchDevicesInPolygon(feature);
    }
  };
  theDrawElementGather.gatherHandlerDestroy();
  theDrawElementGather.polygonGather(gatherPolygonCallback, {
    color: drawElementColor,
    alpha: 0.5,
    viewer: getCesiumEngineInstance('homeMap-fly').viewer,
    elementId: 'mainMapContainer'
  });
};
// 测距
const control_measureLine = () => {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  createMeasureLineHandler();
  const measureLayer = flyView.dataSources.getByName('measureLayer')[0];
  drawLineMeasureGraphics(flyView, measureLayer, measureLineHandler);
};
// 测面
const control_measurePolygon = () => {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  createMeasureLineHandler();
  const measureLayer = flyView.dataSources.getByName('measureLayer')[0];
  drawAreaMeasureGraphics(flyView, measureLayer, measureLineHandler);
};
// 测量工具handler创建
const createMeasureLineHandler = () => {
  measureLineHandler = new Cesium.ScreenSpaceEventHandler(getCesiumEngineInstance('homeMap-fly').viewer.scene.canvas);
};
// 测量工具handler销毁
const destroyMeasureLineHandler = () => {
  if (measureLineHandler) {
    if (!measureLineHandler.isDestroyed()) {
      measureLineHandler = measureLineHandler && measureLineHandler.destroy();
    }
  }
};
// 删除
const control_delete = () => {
  // 清空测量图层
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  destroyMeasureLineHandler();
  const measureLayer = flyView.dataSources.getByName('measureLayer')[0];
  measureLayer.entities.removeAll();
  // 清空空间查询绘制图层
  theDrawElementGather.gatherHandlerDestroy();
  // 鼠标变成默认
  document.getElementById('mainMapContainer').style.cursor = 'default';
  drawElementLayer.entities.removeAll();
  // 清空查询结果图层
  searchResultLayer.entities.removeAll();

  // 清空查询结果列表
  searchPolygonResult.value = [];
  searchResult.value = [];
};

// 控制方法映射
const methodsMap = reactive({
  layerControl: control_lyr,
  mapControl: control_map,
  eyeControl: control_eye,
  searchControl: control_search,
  airportAreaControl: control_airportArea,
  pntControl: control_pnt,
  lineControl: control_line,
  squareControl: control_square,
  circleControl: control_circle,
  polygonControl: control_polygon,
  measureLineControl: control_measureLine,
  measurePolygonControl: control_measurePolygon,
  deleteControl: control_delete
});

// 图层控制
const handleCheckboxChange = lyr => {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  if (lyr === '天地图影像') {
    const imageryLayers = flyView.imageryLayers;
    lyr_img.value = !lyr_img.value;
    lyr_vec.value = !lyr_vec.value;
    imageryLayers.get(0).show = lyr_img.value;
    imageryLayers.get(1).show = lyr_img.value;
    imageryLayers.get(2).show = lyr_vec.value;
    imageryLayers.get(3).show = lyr_vec.value;
    const eyeView = getCesiumEngineInstance('eyeView-fly').viewer;
    if (eyeView) {
      const eyeImageryLayers = eyeView.imageryLayers;
      eyeImageryLayers.get(0).show = lyr_img.value;
      eyeImageryLayers.get(1).show = lyr_img.value;
      eyeImageryLayers.get(2).show = lyr_vec.value;
      eyeImageryLayers.get(3).show = lyr_vec.value;
    }
  } else if (lyr === '天地图矢量') {
    const imageryLayers = flyView.imageryLayers;
    lyr_img.value = !lyr_img.value;
    lyr_vec.value = !lyr_vec.value;
    imageryLayers.get(0).show = lyr_img.value;
    imageryLayers.get(1).show = lyr_img.value;
    imageryLayers.get(2).show = lyr_vec.value;
    imageryLayers.get(3).show = lyr_vec.value;
    const eyeView = getCesiumEngineInstance('eyeView-fly').viewer;
    if (eyeView) {
      const eyeImageryLayers = eyeView.imageryLayers;
      eyeImageryLayers.get(0).show = lyr_img.value;
      eyeImageryLayers.get(1).show = lyr_img.value;
      eyeImageryLayers.get(2).show = lyr_vec.value;
      eyeImageryLayers.get(3).show = lyr_vec.value;
    }
  } else if (lyr === '禁飞区') {
    const noFlyZoneLyr = flyView.dataSources.getByName('noFlyZoneLyr')[0];
    noFlyZoneLyr.show = lyr_noFly.value;
  } else if (lyr === '限高区') {
    const limitHeightLyr = flyView.dataSources.getByName('limitHeightLyr')[0];
    limitHeightLyr.show = lyr_limitHeight.value;
  } else if (lyr === '机场覆盖范围') {
    const airPortAreaLyr = flyView.dataSources.getByName('airPortAreaLyr')[0];
    airPortAreaLyr.show = lyr_airPortArea.value;
  } else if (lyr === '标绘') {
    // 标绘图层控制逻辑
    console.log('标绘图层切换:', lyr_polt.value);
  } else if (lyr === '围栏') {
    fenceLayer.show = lyr_fence.value;
    if (lyr_fence.value && fenceLayer.entities.values.length === 0) {
      loadFenceData();
    }
  } else if (lyr === '自定义限飞区') {
    limitFlightLyr.show = lyr_limitFlightArea.value;
  } else if (lyr === '自定义作业区') {
    workFlightLyr.show = lyr_workFlightArea.value;
  }
};

// 处理自定义动态图层
const handleCustomLyrCheckboxChange = lyr => {
  const dynamicLayer = dynamicLayerSources.get(lyr);
  if (dynamicLayer) {
    const layerData = dynamicLayers.value.find(layer => layer.layerName === lyr);
    if (layerData) {
      dynamicLayer.show = layerData.show;
      if (layerData.show && dynamicLayer.entities.values.length === 0) {
        loadLayerFeatures(layerData.id, dynamicLayer, layerData);
      }
    }
  }
};

// 控制地图和图层面板
const toggleActivation = (target, dependent) => {
  if (dependent.value && target.value !== dependent.value) {
    dependent.value = false;
  }
  target.value = !target.value;
};

// 地图和图层切换
const controlMapLayer = type => {
  const targets = {
    layer: { target: isLayerActive, dependent: isEarthActive },
    earth: { target: isEarthActive, dependent: isLayerActive }
  };

  if (targets[type]) {
    toggleActivation(targets[type].target, targets[type].dependent);
  }
};
// 关闭空间查询结果
function control_SearchPolygon() {
  isSearchPolygonActive.value = false;
}
// 空间查询，构造面
function createTurfolygon(polygonType, gatherElement) {
  let feature = null;
  switch (polygonType) {
    // 点选，100米缓冲区
    case 'pnt':
      const point = turf.point([gatherElement.centerPosition[0], gatherElement.centerPosition[1]]);
      const pointBuff = turf.buffer(point, 100, { units: 'meters' });
      feature = pointBuff;
      break;
    // 圈选
    case 'circle':
      const circlePnt = turf.point([gatherElement.centerPosition[0], gatherElement.centerPosition[1]]);
      const circle = turf.buffer(circlePnt, gatherElement.radius, { units: 'meters' });
      feature = circle;
      break;
    // 多边形、矩形选
    case 'square':
    case 'polygon':
      let points = [];
      gatherElement.gatherPoints.forEach(item => {
        points.push([item.lng, item.lat]);
      });
      // 追加首尾相连的
      points.push([gatherElement.gatherPoints[0].lng, gatherElement.gatherPoints[0].lat]);
      const polygon = turf.polygon([points]);
      feature = polygon;
      break;
    // 线缓冲区，100米
    case 'line':
      let linePoints = [];
      gatherElement.gatherPoints.forEach(item => {
        linePoints.push([item.lng, item.lat]);
      });
      const line = turf.lineString(linePoints);
      const bufferedLine = turf.buffer(line, 100, { units: 'meters' });
      feature = bufferedLine;
      break;
    default:
      break;
  }
  console.log(feature);
  return feature;
}

// 判断机场和无人机是否在面里
function searchDevicesInPolygon(featurePolygon) {
  searchPolygonResult.value = [];
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const entities = flyView.entities.values;
  entities.forEach(item => {
    console.log(item);
    const cartesian3Coordinate = item.position._value;
    if (cartesian3Coordinate) {
      let degreesCoordinate = toDegrees(cartesian3Coordinate);
      const point = turf.point([degreesCoordinate[0], degreesCoordinate[1]]);
      if (turf.booleanPointInPolygon(point, featurePolygon)) {
        isSearchPolygonActive.value = true;
        if (item.id.indexOf('dock_') !== -1) {
          // 机场
          dockList.value.forEach(oneItem => {
            if (oneItem.dock_sn === item.id.split('_')[1]) {
              searchPolygonResult.value.push(oneItem);
            }
          });
        } else if (item.id.indexOf('nav_') !== -1) {
          // 无人机
          navList.value.forEach(oneItem => {
            if (oneItem.nav_sn === item.id.split('_')[1]) {
              let resultItem = {
                name: oneItem.name,
                nav_sn: oneItem.nav_sn,
                lon: degreesCoordinate[0],
                lat: degreesCoordinate[1],
                type: 'nav'
              };
              searchPolygonResult.value.push(resultItem);
            }
          });
        }
        // 警情
        else if (item.id.indexOf('firePnt_') !== -1) {
          let resultItem = {
            name: item.id.split('_')[1],
            lon: degreesCoordinate[0],
            lat: degreesCoordinate[1],
            type: 'fire'
          };
          searchPolygonResult.value.push(resultItem);
        }
      }
    }
  });
}

// 搜索切换
const searchTypeChangeHandle = value => {
  searchType.value = value;
};

// 搜索
const searchHandle = () => {
  searchResult.value = [];
  if (searchName.value !== '') {
    switch (searchType.value) {
      // 机场
      case 0:
        findDockList(searchName.value);
        break;
      // 无人机
      case 1:
        findNavList(searchName.value);
        break;
      // 警情
      case 2:
        findAlarmList(searchName.value);
        break;
    }
  }
};

// 初始化机场、无人机列表
function initDeviceList() {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page: 1,
    page_size: 100
  }).then(data => {
    const { list } = data;
    if (list.length > 0) {
      list.forEach(item => {
        let oneItem = {
          name: item.nickname, //机场名
          dock_sn: item.device_sn, //机场SN号
          lon: item.longitude, //机场经纬度
          lat: item.latitude,
          type: 'dock'
        };
        dockList.value.push(oneItem);
      });
    }
  });

  getDevicesBound({
    domain: DOMAIN.DRONE,
    page: 1,
    page_size: 100
  }).then(data => {
    const { list } = data;
    if (list.length > 0) {
      list.forEach(item => {
        let oneItem = {
          name: item.nickname, //无人机名
          nav_sn: item.device_sn, //无人机SN号
          type: 'nav'
        };
        navList.value.push(oneItem);
      });
    }
  });
}
// 机场检索
const findDockList = name => {
  if (dockList.value.length > 0) {
    dockList.value.forEach(dock => {
      if (dock.name.indexOf(name) > -1) {
        searchResult.value.push(dock);
      }
    });
  }
};

// 无人机检索
const findNavList = name => {
  if (navList.value.length > 0) {
    navList.value.forEach(nav => {
      if (nav.name.indexOf(name) > -1) {
        searchResult.value.push(nav);
      }
    });
  }
};
// 警情检索
function findAlarmList(name) {
  getJJTaskPage({
    alarm_name: name,
    page: 1,
    page_size: 1000
  }).then(data => {
    const { list } = data;
    let alarmList = list || [];
    if (alarmList.length > 0) {
      console.log(alarmList);
      alarmList.forEach(alarm => {
        searchResult.value.push({
          name: alarm.alarm_name,
          lon: alarm.longitude,
          lat: alarm.latitude,
          type: 'fire'
        });
      });
    }
  });
}

// 跳转定位查询结果
function flyToSearchPnt(item) {
  // 清空查询结果图层
  searchResultLayer.entities.removeAll();
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  if (item.type === 'fire') {
    const position = Cesium.Cartesian3.fromDegrees(item.lon, item.lat, 10);
    searchResultLayer.entities.add({
      position: position,
      billboard: {
        show: true,
        image: firePnt_url,
        width: 40,
        height: 52,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      }
    });
  }
  if (item.type === 'nav') {
    if (flyView.entities.getById('nav_' + item.nav_sn) !== undefined) {
      const navEntity = flyView.entities.getById('nav_' + item.nav_sn);
      const pnt = toDegrees(navEntity.position._value);
      flyTo(flyView, pnt[0], pnt[1], 5000, 0, -90, 0, 1);
    } else {
      ElMessage.warning(`${item.name} 处于离线状态`);
    }
  } else {
    flyTo(flyView, item.lon, item.lat, 5000, 0, -90, 0, 1);
  }
}

//#region 辅助方法

// 生成唯一ID
const generateKey = () => {
  return 'id_' + Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
};

// 设置图层的可见级别控制
const setupLayerLevelControl = (dataSource, minLevel, maxLevel, viewer) => {
  // 监听相机高度变化来控制图层可见性
  const updateLayerVisibility = () => {
    const camera = viewer.camera;
    const height = camera.positionCartographic.height;

    // 根据高度计算当前级别（简化的级别计算）
    let currentLevel = 1;
    if (height > 20000000) currentLevel = 1;
    else if (height > 10000000) currentLevel = 2;
    else if (height > 5000000) currentLevel = 3;
    else if (height > 2000000) currentLevel = 4;
    else if (height > 1000000) currentLevel = 5;
    else if (height > 500000) currentLevel = 6;
    else if (height > 250000) currentLevel = 7;
    else if (height > 100000) currentLevel = 8;
    else if (height > 50000) currentLevel = 9;
    else if (height > 25000) currentLevel = 10;
    else if (height > 12000) currentLevel = 11;
    else if (height > 6000) currentLevel = 12;
    else if (height > 3000) currentLevel = 13;
    else if (height > 1500) currentLevel = 14;
    else if (height > 750) currentLevel = 15;
    else if (height > 375) currentLevel = 16;
    else if (height > 188) currentLevel = 17;
    else currentLevel = 18;

    // 根据当前级别和图层的min/max级别控制可见性
    const shouldShow = currentLevel >= minLevel && currentLevel <= maxLevel;
    if (dataSource.show !== shouldShow && dataSource.entities.values.length > 0) {
      dataSource.show = shouldShow;
    }
  };

  // 添加相机移动监听器
  viewer.camera.moveEnd.addEventListener(updateLayerVisibility);
  viewer.camera.changed.addEventListener(updateLayerVisibility);

  // 初始检查
  updateLayerVisibility();
};

// 加载自定义限飞区、作业区数据
const loadManMadeNoFlyZoneData = async () => {
  let list = [];
  getFlightDataList().then(res => {
    if (!res || res.length === 0) {
      return;
    }
    list = res;
    list.forEach(item => {
      if (item.type === FLIGHTAREATYPE.NFZ) {
        item.content.properties.color = '#FF4500';
      } else if (item.type === FLIGHTAREATYPE.DFENCE) {
        item.content.properties.color = '#2DFFDC';
      }
      if (item.type === FLIGHTAREATYPE.DFENCE) {
        if (item.content.geometry.type === GEOMTYPE.CIRCLE || item.content.geometry.type === 'Circle') {
          if (item.status) {
            // 创建圆形
            let circleEntity = createCircle(item);
            workFlightLyr.entities.add(circleEntity);
          }
        } else if (item.content.geometry.type === GEOMTYPE.POLYGON || item.content.geometry.type === 'Polygon') {
          if (item.status) {
            let polygonEntity = createPolygon(item);
            workFlightLyr.entities.add(polygonEntity);
          }
        }
      } else if (item.type === FLIGHTAREATYPE.NFZ) {
        if (item.content.geometry.type === GEOMTYPE.CIRCLE || item.content.geometry.type === 'Circle') {
          if (item.status) {
            // 创建圆形
            let circleEntity = createCircle(item);
            limitFlightLyr.entities.add(circleEntity);
          }
        } else if (item.content.geometry.type === GEOMTYPE.POLYGON || item.content.geometry.type === 'Polygon') {
          if (item.status) {
            let polygonEntity = createPolygon(item);
            limitFlightLyr.entities.add(polygonEntity);
          }
        }
      }
    });
  });
};

const createCircle = (options = null) => {
  if (!options) {
    return;
  }
  let radius = options.content.geometry.radius;
  const semiMinorAxisCb = new Cesium.CallbackProperty(function () {
    return radius || 1;
  }, false);

  let color = new Cesium.Color.fromCssColorString(options.content.properties.color).withAlpha(0.4);
  let entity = new Cesium.Entity({
    id: options.area_id || generateKey(),
    position: new Cesium.Cartesian3.fromDegrees(...options.content.geometry.coordinates, 0),
    name: 'circle',
    label: {
      text: options.name || '',
      font: '14px sans-serif',
      style: Cesium.LabelStyle.FILL_AND_OUTLINE, //FILL  FILL_AND_OUTLINE OUTLINE
      fillColor: Cesium.Color.WHITE,
      showBackground: true, //指定标签后面背景的可见性
      backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
      backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
      pixelOffset: new Cesium.Cartesian2(0, -25),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    },
    ellipse: {
      material: color || Cesium.Color.WHITE.withAlpha(0.4),
      outline: true,
      outlineColor: Cesium.Color.RED,
      outlineWidth: 1, // 是否被提供的材质填充
      fill: true,
      semiMajorAxis: semiMinorAxisCb,
      semiMinorAxis: semiMinorAxisCb,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
    }
  });
  return entity;
};

const createPolygon = (options = null) => {
  if (!options) {
    return;
  }
  let positions = toRaw(options.content.geometry.coordinates[0]);
  const cb = new Cesium.CallbackProperty(function () {
    let t = arrayToCartesian3(positions);
    let arrPoint = new Cesium.PolygonHierarchy(t);
    return arrPoint;
  }, false);
  const center = getCenterCoordinates(positions);
  let color = new Cesium.Color.fromCssColorString(options.content.properties.color).withAlpha(0.4);
  // 根据点位置创建面
  let entity = new Cesium.Entity({
    id: options.area_id || generateKey(),
    name: 'polygon',
    position: toCartesian3(center),
    label: {
      text: options.name || '',
      font: '14px sans-serif',
      style: Cesium.LabelStyle.FILL_AND_OUTLINE, //FILL  FILL_AND_OUTLINE OUTLINE
      fillColor: Cesium.Color.WHITE,
      showBackground: true, //指定标签后面背景的可见性
      backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
      backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
      pixelOffset: new Cesium.Cartesian2(0, -25),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    },
    polygon: {
      hierarchy: cb,
      show: true,
      fill: true,
      material: color || Cesium.Color.WHITE.withAlpha(0.4),
      outline: true,
      outlineColor: Cesium.Color.ALICEBLUE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
  });
  return entity;
};

//#endregion

onMounted(() => {
  setTimeout(() => {
    const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
    flyView.dataSources.add(drawElementLayer);
    flyView.dataSources.add(searchResultLayer);

    // 自定义限飞区、作业区
    limitFlightLyr.show = lyr_limitFlightArea.value;
    flyView.dataSources.add(limitFlightLyr);
    workFlightLyr.show = lyr_workFlightArea.value;
    flyView.dataSources.add(workFlightLyr);

    // 围栏图层
    fenceLayer.show = lyr_fence.value;
    flyView.dataSources.add(fenceLayer);

    // 加载自定义限飞区和工作区数据
    loadManMadeNoFlyZoneData();

    // 加载动态图层
    loadDynamicLayers();
  }, 200);

  initDeviceList();
});

// 加载围栏数据
const loadFenceData = async () => {
  try {
    const response = await getFenceList({ keyword: '' });
    const fences = response || [];

    fences.forEach(fence => {
      if (fence.fence_status !== 'active') {
        return;
      }
      if (fence.fence_coords && fence.fence_coords.length > 0) {
        let coords = fence.fence_coords;
        if (typeof fence.fence_coords === 'string') {
          coords = JSON.parse(fence.fence_coords);
        }

        const center = getCenterCoordinates(coords);
        const positions = coords.map(coord => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));

        let fenceColor = Cesium.Color.fromCssColorString('#74E73B');
        let outlineColor = Cesium.Color.fromCssColorString('#74E73B');

        // 创建立体围栏多面体
        fenceLayer.entities.add({
          id: `fence_${fence.id}`,
          name: fence.fence_name,
          position: toCartesian3(center),
          polygon: {
            hierarchy: new Cesium.PolygonHierarchy(positions),
            height: 0, // 底面高度
            extrudedHeight: 500, // 拉伸高度500米
            material: fenceColor.withAlpha(0.15), // 中间透明
            outline: true,
            outlineColor: outlineColor,
            outlineWidth: 2,
            extrudedHeightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
            closeTop: false, // 顶面透明
            closeBottom: true, // 底面显示
            fill: true
          },
          label: {
            text: fence.fence_name,
            font: '14px sans-serif',
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            showBackground: true,
            backgroundColor: Cesium.Color.BLACK.withAlpha(0.6),
            backgroundPadding: new Cesium.Cartesian2(8, 4),
            pixelOffset: new Cesium.Cartesian2(0, -60),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
          },
          properties: {
            type: 'fence',
            fenceType: fence.fence_type,
            businessType: fence.business_type,
            contact: fence.contact,
            telephone: fence.telephone,
            status: fence.fence_status,
            height: 500
          }
        });
      }
    });
  } catch (error) {
    console.error('加载围栏数据失败:', error);
  }
};

// 创建点实体
const createPointEntity = (geometry, layerConfig) => {
  const position = Cesium.Cartesian3.fromDegrees(
    geometry.coordinates[0],
    geometry.coordinates[1],
    geometry.coordinates[2] || 0
  );

  // 如果配置了图标
  if (layerConfig.icon) {
    return {
      position: position,
      billboard: {
        image: layerConfig.icon,
        width: layerConfig.size ? layerConfig.size[0] : 32,
        height: layerConfig.size ? layerConfig.size[1] : 32,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      }
    };
  } else {
    // 使用点样式
    return {
      position: position,
      point: {
        pixelSize: layerConfig.size || 12,
        color: Cesium.Color.fromCssColorString(layerConfig.color || '#FF0000'),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      }
    };
  }
};

// 创建线实体
const createLineEntity = (geometry, layerConfig) => {
  const positions = geometry.coordinates.map(coord => Cesium.Cartesian3.fromDegrees(coord[0], coord[1], coord[2] || 0));

  return {
    polyline: {
      positions: positions,
      width: layerConfig.borderWidth || 3,
      material: Cesium.Color.fromCssColorString(layerConfig.fillColor || '#00FF00'),
      clampToGround: true
    }
  };
};

// 创建多边形实体
const createPolygonEntity = (geometry, layerConfig) => {
  const positions = geometry.coordinates[0].map(coord =>
    Cesium.Cartesian3.fromDegrees(coord[0], coord[1], coord[2] || 0)
  );

  return {
    polygon: {
      hierarchy: new Cesium.PolygonHierarchy(positions),
      material: Cesium.Color.fromCssColorString(layerConfig.fillColor || '#0000FF'),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    polyline: {
      positions: positions,
      width: layerConfig.borderWidth || 3,
      material: Cesium.Color.fromCssColorString(layerConfig.borderColor || '#00FF00'),
      clampToGround: true
    }
  };
};

// 加载图层要素数据
const loadLayerFeatures = async (layerId, dataSource, layerInfo) => {
  try {
    const features = await getFeaturesByLayer(layerId);

    if (!features || features.length === 0) {
      console.log(`图层 ${layerInfo.layerName} 暂无要素数据`);
      return;
    }

    // 解析图层配置
    let layerConfig = {};
    try {
      layerConfig = layerInfo.layerConfig ? JSON.parse(layerInfo.layerConfig) : {};
    } catch (e) {
      console.error('解析图层配置失败:', e);
    }

    // 解析标签字体配置
    let labelFont = { size: 12, family: 'Arial' };
    try {
      labelFont = layerInfo.labelFont ? JSON.parse(layerInfo.labelFont) : labelFont;
    } catch (e) {
      console.error('解析标签字体配置失败:', e);
    }

    features.forEach(feature => {
      let entity = null;

      // 根据图层类型处理不同的图元数据
      switch (layerInfo.layerType) {
        case 'point':
          // 点图元：使用longitude和latitude字段
          if (feature.longitude && feature.latitude) {
            const geometry = {
              type: 'Point',
              coordinates: [feature.longitude, feature.latitude, 0]
            };
            entity = createPointEntity(geometry, layerConfig);
          }
          break;
        case 'line':
          // 线图元：使用featureCoords字段
          if (feature.feature_coords) {
            try {
              const coords =
                typeof feature.feature_coords === 'string'
                  ? JSON.parse(feature.feature_coords)
                  : feature.feature_coords;

              if (coords && coords.length > 1) {
                const geometry = {
                  type: 'LineString',
                  coordinates: coords
                };
                entity = createLineEntity(geometry, layerConfig);
                const center = getCenterCoordinates(coords);
                entity.position = toCartesian3(center);
              }
            } catch (e) {
              console.error('解析线图元坐标失败:', e);
            }
          }
          break;
        case 'polygon':
          // 面图元：使用featureCoords字段
          if (feature.feature_coords) {
            try {
              const coords =
                typeof feature.feature_coords === 'string'
                  ? JSON.parse(feature.feature_coords)
                  : feature.feature_coords;

              if (coords && coords.length > 2) {
                // 确保多边形是闭合的
                const polygonCoords = [...coords];
                if (
                  polygonCoords[0][0] !== polygonCoords[polygonCoords.length - 1][0] ||
                  polygonCoords[0][1] !== polygonCoords[polygonCoords.length - 1][1]
                ) {
                  polygonCoords.push(polygonCoords[0]);
                }

                const geometry = {
                  type: 'Polygon',
                  coordinates: [polygonCoords]
                };
                entity = createPolygonEntity(geometry, layerConfig);
                const center = getCenterCoordinates(polygonCoords);
                entity.position = toCartesian3(center);
              }
            } catch (e) {
              console.error('解析面图元坐标失败:', e);
            }
          }
          break;
      }

      if (entity) {
        entity.id = `feature_${feature.id}`;
        entity.name = feature.feature_name;

        // 添加标签（根据label_field配置）
        if (layerInfo.labelField && feature[layerInfo.labelField]) {
          entity.label = {
            text: feature[layerInfo.labelField],
            font: `${labelFont.size}px ${labelFont.family}`,
            fillColor: Cesium.Color.fromCssColorString(layerInfo.labelColor || '#FFFFFF'),
            showBackground: true,
            backgroundColor: Cesium.Color.BLACK.withAlpha(0.6),
            pixelOffset: new Cesium.Cartesian2(0, -50),
            disableDepthTestDistance: Number.POSITIVE_INFINITY
          };
        }

        // 添加属性信息
        entity.properties = {
          type: 'feature',
          layerId: layerId,
          featureId: feature.id,
          featureName: feature.feature_name || feature.name,
          featureAddr: feature.feature_addr,
          contact: feature.contact,
          telephone: feature.telephone,
          description: feature.remark,
          layerType: layerInfo.layerType,
          extraInfo: feature.extra_info
        };

        dataSource.entities.add(entity);
      }
    });
  } catch (error) {
    console.error('加载图层要素失败:', error);
  }
};

// 加载动态图层列表
const loadDynamicLayers = async () => {
  try {
    const response = await getAllLayers();
    const layers = response.data?.list || response.list || response || [];

    const flyView = getCesiumEngineInstance('homeMap-fly').viewer;

    // 过滤并处理图层数据，按sort_order排序
    dynamicLayers.value = layers
      .filter(layer => layer.is_visible && layer.layer_name)
      .map(layer => ({
        id: layer.id,
        layerName: layer.layer_name,
        layerType: layer.layer_type,
        show: false,
        minLevel: layer.min_level || 1,
        maxLevel: layer.max_level || 18,
        sortOrder: layer.sort_order || 0,
        labelField: layer.label_field,
        labelColor: layer.label_color,
        labelFont: layer.label_font,
        layerConfig: layer.layer_config,
        remark: layer.remark || ''
      }))
      .sort((a, b) => a.sortOrder - b.sortOrder); // 按sort_order排序

    // 为每个图层创建数据源
    dynamicLayers.value.forEach(layer => {
      const dataSource = new Cesium.CustomDataSource(layer.layerName);
      dataSource.show = layer.show;

      // 设置图层的可见级别控制
      setupLayerLevelControl(dataSource, layer.minLevel, layer.maxLevel, flyView);

      dynamicLayerSources.set(layer.layerName, dataSource);
      flyView.dataSources.add(dataSource);
    });

    console.log(`成功加载 ${dynamicLayers.value.length} 个动态图层`);
  } catch (error) {
    console.error('加载动态图层失败:', error);
    dynamicLayers.value = [];
  }
};
</script>

<template>
  <div class="mapTools">
    <!-- 工具条 -->
    <div style="padding-top: 5px">
      <div
        v-for="(tool, index) in tools"
        :key="index"
        class="tools-item"
        :class="{ 'is-active': toolClicks[tool.name] }"
        @click="selectTool(tool.name)"
      >
        <el-tooltip :content="tool.cn" placement="left">
          <template #default>
            <svg-icon :icon-class="`t_${tool.icon}`" class="icon-svg" />
          </template>
        </el-tooltip>
      </div>
    </div>
    <!-- 图层管理 -->
    <div class="lyr-manager" v-if="isLayerActive" :class="{ 'is-active': !showPoltLyr }">
      <div class="lyr-manager-title">
        <el-row>
          <el-col :span="2">
            <svg-icon icon-class="layers" class="title-icon" />
          </el-col>
          <el-col :span="16" style="font-size: 16px; color: #fff; font-weight: bold">图层管理</el-col>
          <el-col :span="6" style="text-align: right; cursor: pointer">
            <svg-icon icon-class="close" class="close-icon" @click="controlMapLayer('layer')" />
          </el-col>
        </el-row>
      </div>

      <div class="lyr-manager-content" :class="{ 'is-active': !showPoltLyr }">
        <!-- 业务图层 -->
        <div class="layer-group">
          <div class="layer-group-title">
            <svg-icon icon-class="business" class="group-icon" />
            <span>业务图层</span>
          </div>
          <div class="layer-items">
            <div class="layer-item">
              <el-checkbox v-model="lyr_noFly" @change="handleCheckboxChange('禁飞区')">
                <span class="layer-label">禁飞区</span>
              </el-checkbox>
              <div class="layer-status" :class="{ active: lyr_noFly }"></div>
            </div>
            <div class="layer-item">
              <el-checkbox v-model="lyr_limitHeight" @change="handleCheckboxChange('限高区')">
                <span class="layer-label">限高区</span>
              </el-checkbox>
              <div class="layer-status" :class="{ active: lyr_limitHeight }"></div>
            </div>
            <div class="layer-item">
              <el-checkbox v-model="lyr_limitFlightArea" @change="handleCheckboxChange('自定义限飞区')">
                <span class="layer-label">自定义限飞区</span>
              </el-checkbox>
              <div class="layer-status" :class="{ active: lyr_limitFlightArea }"></div>
            </div>
            <div class="layer-item">
              <el-checkbox v-model="lyr_workFlightArea" @change="handleCheckboxChange('自定义作业区')">
                <span class="layer-label">自定义作业区</span>
              </el-checkbox>
              <div class="layer-status" :class="{ active: lyr_workFlightArea }"></div>
            </div>
            <div class="layer-item">
              <el-checkbox v-model="lyr_fence" @change="handleCheckboxChange('围栏')">
                <span class="layer-label">围栏</span>
              </el-checkbox>
              <div class="layer-status" :class="{ active: lyr_fence }"></div>
            </div>
          </div>
        </div>

        <!-- 动态图层 -->
        <div class="layer-group" v-if="dynamicLayers.length > 0">
          <div class="layer-group-title">
            <svg-icon icon-class="custom" class="group-icon" />
            <span>自定义图层</span>
          </div>
          <div class="layer-items">
            <div class="layer-item" v-for="layer in dynamicLayers" :key="layer.id">
              <el-checkbox v-model="layer.show" @change="handleCustomLyrCheckboxChange(layer.layerName)">
                <span class="layer-label">{{ layer.layerName }}</span>
              </el-checkbox>
              <div class="layer-status" :class="{ active: layer.show }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 底图切换 -->
    <div class="map-manager" v-if="isEarthActive">
      <div class="map-manager-title">
        <el-row>
          <el-col :span="2"></el-col>
          <el-col :span="10" style="font-size: 14px">地图</el-col>
          <el-col :span="11" style="text-align: right; cursor: pointer">
            <svg-icon icon-class="close" class="sc-item-svg" @click="controlMapLayer('earth')" />
          </el-col>
          <el-col :span="1"></el-col>
        </el-row>
      </div>
      <div class="map-manager-content">
        <div class="mmc-item">
          <div class="map-img" :class="{ 'is-active': lyr_img }" @click="handleCheckboxChange('天地图影像')"></div>
          <div class="mmc-item-title" :class="{ 'is-active': lyr_img }">影像</div>
        </div>
        <div class="mmc-item">
          <div class="vec-img" :class="{ 'is-active': lyr_vec }" @click="handleCheckboxChange('天地图矢量')"></div>
          <div class="mmc-item-title" :class="{ 'is-active': lyr_vec }">矢量</div>
        </div>
      </div>
    </div>
    <!-- 鹰眼 -->
    <div class="map-eye" v-if="isEyeActive">
      <div class="map-eye-title">
        <el-row>
          <el-col :span="2"><svg-icon icon-class="title" style="width: 16px; height: 16px; margin: 10px" /></el-col>
          <el-col :span="10" style="font-size: 14px">鹰眼</el-col>
          <el-col :span="11" style="text-align: right; cursor: pointer">
            <svg-icon icon-class="close" class="sc-item-svg" @click="control_eye()" />
          </el-col>
        </el-row>
      </div>
      <div class="map-eye-content" id="map-eye-content"></div>
    </div>
    <!-- 查询 -->
    <div class="search-box" v-if="isSearchActive">
      <div class="search-box-title">
        <el-row>
          <el-col :span="10">
            <div>
              <el-select v-model="searchType" placeholder="请选择" style="width: 100%" :clearable="false">
                <el-option
                  v-for="item in searchTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  @change="searchTypeChangeHandle"
                />
              </el-select>
            </div>
          </el-col>
          <el-col :span="1"></el-col>
          <el-col :span="12" style="font-size: 14px">
            <el-input
              v-model="searchName"
              style="width: 100%"
              :maxlength="32"
              placeholder="请输入查询名称"
              @input="searchHandle"
            />
          </el-col>
        </el-row>
      </div>
      <div class="search-box-content">
        <div class="null_data" v-if="searchResult.length == 0"><div class="null_data_text">暂无数据</div></div>
        <el-scrollbar v-else>
          <div class="search-box-item" v-for="(item, index) in searchResult" :key="index">
            <div class="search-box-item-title" @click="flyToSearchPnt(item)">{{ item.name }}</div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <!-- 空间查询结果 -->
    <div class="search-box" v-if="isSearchPolygonActive">
      <div class="search-box-title-2">
        <el-row>
          <el-col :span="1"></el-col>
          <el-col :span="11" style="font-size: 14px">空间查询结果</el-col>
          <el-col :span="11" style="text-align: right; cursor: pointer">
            <svg-icon icon-class="close" class="sc-item-svg" @click="control_SearchPolygon()" />
          </el-col>
        </el-row>
      </div>
      <div class="search-box-content">
        <div class="null_data" v-if="searchPolygonResult.length == 0"><div class="null_data_text">暂无数据</div></div>
        <el-scrollbar v-else>
          <div class="search-box-item" v-for="(item, index) in searchPolygonResult" :key="index">
            <div class="search-box-item-title" @click="flyToSearchPnt(item)">
              <img
                v-show="item.type == 'dock'"
                src="@/assets/gis/tools/dock.png"
                style="width: 24px; height: 24px; margin: 3px 3px 3px 0px"
              />
              <img
                v-show="item.type == 'nav'"
                src="@/assets/gis/tools/nav.png"
                style="width: 24px; height: 24px; margin: 3px 3px 3px 0px"
              />
              <img
                v-show="item.type == 'fire'"
                src="@/assets/gis/tools/fire.png"
                style="width: 24px; height: 24px; margin: 3px 3px 3px 0px"
              />
              {{ item.name }}
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mapTools {
  background: rgba(0, 17, 41, 0.5);
  background-image: url('@/assets/gis/tools/tools_back.png');
  border-radius: 6px;
  width: 50px;
  height: 560px;
  // display: grid;
  .tools-item {
    margin: 4px 6px;
    width: 38px;
    height: 38px;
    background-image: url('@/assets/gis/tools/btn2.png');
    cursor: pointer;
    &:hover {
      background-image: url('@/assets/gis/tools/btn_select2.png');
    }
    &.is-active {
      background-image: url('@/assets/gis/tools/btn_select2.png');
    }
    .icon-svg {
      width: 24px !important;
      height: 24px !important;
      margin: 7px;
      color: #fff;
    }
  }
}
.lyr-manager {
  position: absolute;
  top: 0px;
  right: 60px;
  background: linear-gradient(135deg, #001129 0%, #002147 100%);
  border: 1px solid #1a3a5c;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  z-index: 2;
  width: 280px;
  min-height: 200px;
  max-height: 500px;
  overflow: hidden;
  backdrop-filter: blur(10px);

  &.is-active {
    min-height: 220px;
  }
}

.lyr-manager-title {
  width: 100%;
  height: 45px;
  background: linear-gradient(135deg, #11253e 0%, #1a3a5c 100%);
  border-bottom: 1px solid #2a4a6c;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  color: #ffffff;
  line-height: 45px;
  padding: 0 15px;

  .title-icon {
    width: 18px;
    height: 18px;
    color: #4a9eff;
    margin-top: 13px;
  }

  .close-icon {
    width: 16px;
    height: 16px;
    color: #8fa8c4;
    margin-top: 14px;
    transition: color 0.3s ease;

    &:hover {
      color: #ff6b6b;
    }
  }
}

.lyr-manager-content {
  padding: 15px;
  max-height: 440px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(74, 158, 255, 0.6);
    border-radius: 3px;

    &:hover {
      background: rgba(74, 158, 255, 0.8);
    }
  }
}

.layer-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.layer-group-title {
  display: flex;
  align-items: center;
  font-size: 13px;
  font-weight: bold;
  color: #4a9eff;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(74, 158, 255, 0.2);

  .group-icon {
    width: 14px;
    height: 14px;
    margin-right: 8px;
    color: #4a9eff;
  }
}

.layer-items {
  .layer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(74, 158, 255, 0.1);
    }

    .layer-label {
      font-size: 12px;
      color: #e1e8f0;
      margin-left: 4px;
    }

    .layer-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .layer-status {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #666;
      transition: background-color 0.3s ease;

      &.active {
        background-color: #4a9eff;
        box-shadow: 0 0 8px rgba(74, 158, 255, 0.6);
      }
    }

    .info-icon {
      width: 12px;
      height: 12px;
      color: #8fa8c4;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #4a9eff;
      }
    }
  }
}

// 自定义复选框样式
:deep(.el-checkbox) {
  .el-checkbox__input {
    .el-checkbox__inner {
      background-color: transparent;
      border-color: #4a9eff;
      width: 14px;
      height: 14px;

      &:hover {
        border-color: #6bb6ff;
      }
    }

    &.is-checked .el-checkbox__inner {
      background-color: #4a9eff;
      border-color: #4a9eff;

      &::after {
        border-color: #fff;
        border-width: 1px;
      }
    }
  }

  .el-checkbox__label {
    color: #e1e8f0;
    font-size: 12px;
    padding-left: 8px;
  }
}
.map-manager {
  position: absolute;
  top: 40px;
  right: 60px;
  background-color: #001129;
  z-index: 2;
  border-radius: 2px;
  width: 156px;
  height: 150px;
}

.map-manager-title {
  width: 156px;
  height: 38px;
  background: #11253e;
  font-family: SourceHanSansSC-Bold;
  font-size: 12px;
  color: #ffffff;
  line-height: 36px;
}

.map-manager-content {
  display: flex;
  height: 112px;
  width: 156px;
  .mmc-item {
    width: 50%;
  }

  .mmc-item-title {
    color: #fff;
    font-size: 13px;
    text-align: center;
    padding-top: 10px;
    &.is-active {
      color: #2e90fa;
    }
  }

  .map-img {
    width: 54px;
    height: 54px;
    border-radius: 2px;
    background-image: url('@/assets/gis/img.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-left: 12px;
    margin-top: 12px;
    cursor: pointer;
    &.is-active {
      border: 1px solid #2e90fa;
    }
  }
  .vec-img {
    width: 54px;
    height: 54px;
    border-radius: 2px;
    background-image: url('@/assets/gis/vec.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-left: 12px;
    margin-top: 12px;
    cursor: pointer;
    &.is-active {
      border: 1px solid #2e90fa;
    }
  }
}

.map-eye {
  position: absolute;
  bottom: 0px;
  right: -10px;
  background-color: #001129;
  z-index: 2;
  border-radius: 2px;
  width: 416px;
  height: 272px;
  .map-eye-title {
    width: 416px;
    height: 38px;
    background: #11253e;
    font-family: SourceHanSansSC-Bold;
    font-size: 14px;
    color: #ffffff;
    line-height: 36px;
  }
  .map-eye-content {
    margin: 5px;
    width: 406px;
    height: 224px;
  }
}

.search-box {
  position: absolute;
  top: 0px;
  right: 60px;
  background-color: #001129;
  z-index: 2;
  width: 326px;
  height: 446px;
  font-family: SourceHanSansSC-Bold;
  .search-box-title {
    width: 326px;
    height: 38px;
    padding-left: 10px;
    padding-top: 5px;
    font-size: 14px;
    color: #ffffff;
    line-height: 36px;
  }
  .search-box-title-2 {
    width: 326px;
    height: 38px;
    background: #11253e;
    font-family: SourceHanSansSC-Bold;
    font-size: 14px;
    color: #ffffff;
    line-height: 36px;
  }
  .search-box-content {
    width: 306px;
    height: 398px;
    overflow-y: auto;
    margin: 10px;
    .search-box-item {
      width: 306px;
      background-color: #11253e;
      margin-bottom: 10px;
      border-radius: 2px;
      cursor: pointer;
    }
    .search-box-item-title {
      width: 300px;
      min-height: 30px;
      line-height: 30px;
      font-size: 14px;
      color: #ffffff;
      padding-left: 10px;
      display: flex;
      // overflow: hidden; /* 隐藏超出的内容 */
      // text-overflow: ellipsis; /* 显示省略号 */
      // white-space: nowrap; /* 不换行 */
    }
    .null_data {
      width: 120px;
      height: 102px;
      margin-top: 100px;
      margin-left: 90px;
      background-image: url('@/assets/gis/tools/null_data.png');
      .null_data_text {
        text-align: center;
        color: #fff;
        line-height: 28px;
        padding-top: 120px;
      }
    }
  }
}
</style>
