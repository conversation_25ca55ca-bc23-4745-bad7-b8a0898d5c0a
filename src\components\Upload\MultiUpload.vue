<!--
  多图上传组件
  @author: youlaitech
  @date 2022/11/20
-->

<template>
  <el-upload :class="{
    uoloadSty: limit != fileList.length,
    disUoloadSty: limit == fileList.length,
    uploadList: true,
    hideDelete: !showDelete
  }" v-model:file-list="fileList" :before-upload="handleBeforeUpload" :http-request="handleUpload"
    :on-remove="handleRemove" :on-preview="previewImg" :on-exceed="handleExceed" :list-type="listType" :limit="limit"
    v-loading="uploading" :accept="accept" :videoType="videoType" :imageType="imageType" :before-remove="handelBeforeRemove">
    <el-button v-if="listType === 'text'" type="primary" :disabled="disabled">点击上传</el-button>
    <i-ep-plus v-else/>
  </el-upload>
  <ImgVideoPreview v-model:visible="previewDialog.visible" :title="previewDialog.title" :imgUrl="previewDialog.url" />
</template>

<script setup>
import { fileUpload } from '@/api/common/index';
import { downLoadFile } from '@/utils/common';
import { onMounted, ref } from 'vue';
import { ElMessage } from 'element-plus';
import ImgVideoPreview from './ImgVideoPreview.vue';
const emit = defineEmits(['update:modelValue']);
const uploading = ref(false);
const props = defineProps({
  /**
   * 文件路径集合
   */
  modelValue: {
    type: Array,
    default: () => {
      [];
    }
  },
  /**
   * 文件上传数量限制
   */
  limit: {
    type: Number,
    default: 5
  },
  /**
   * 文件列表的类型
   */
  listType: {
    type: String,
    default: 'text'
  },
  /**
   * 文件类型
   */
  accept: {
    type: String,
    default: ''
  },
  /**
   * 视频类型
   */
  videoType: {
    type: Boolean,
    default: false
  },
  /**
   * 图片类型
   */
  imageType: {
    type: Boolean,
    default: false
  },
  /**
   * 组件引用来源
   */
  from: {
    type: String,
    default: ''
  },
  /**
   * 是否禁用 禁用会隐藏上传按钮
   */
   disabled: {
    type: Boolean,
    default: false
  },
  /**
   * 删除提示语
  */
  tips:{
    type: String,
    default:''
  },
  /**
   * 删除按钮
  */
  showDelete:{
    type: Boolean,
    default:true
  },
  /**
   * 附件类型
  */
  attachmentType:{
    type: String,
    default:''
  },
});

const fileList = ref([]);
const previewDialog = reactive({
  visible: false,
  title: '图片详情',
  url: ''
});
watch(
  () => props.modelValue,
  (newVal, oldVal) => {
    if (!newVal || newVal == oldVal) return;
    fileList.value = newVal || [];
  },
  { immediate: true }
);
watch(
  () => props.disabled,
  (newVal, oldVal) => {
    if(newVal) {
      // document.getElementsByClassName('el-upload').style.display = 'none'
    }
  },
  { immediate: true }
);

/**
 * 自定义图片上传
 *
 * @param params
 */
async function handleUpload(options) {
  
  // 上传API调用
  let formData1 = new FormData();
  formData1.append('file', options.file);
  formData1.append('attachmentType', props.attachmentType);
  uploading.value = true;
  fileUpload(formData1)
    .then(({ code,data,message }) => {
      if(code == '200'){
        let fileUrl =
        data.accessPath.replace('/file/', '') + '?reqType=file';
        // 上传成功需手动替换文件路径为远程URL，否则图片地址为预览地址 blob:http://
        const fileIndex = fileList.value.findIndex(
          file => file.uid == options.file.uid
        );
        fileList.value.splice(fileIndex, 1, {
          name: data.file_name,
          url: data.file_url,
          id: data.file_id
        });
        emit('update:modelValue', fileList.value);
        emit('afterUpload', fileList.value);
      }else {
        emit('update:modelValue', []);
        emit('afterUpload', []);
        ElMessage.error(message);
      }
    }).catch(()=>{
      emit('update:modelValue', []);
      emit('afterUpload', []);
    })
    .finally(() => {
      uploading.value = false;
    });
}

/**
 * 超出限制
 */
function handleExceed() {
  ElMessage.error(`最多上传${props.limit}个文件`);
  return;
}
/**
 * 删除图片之前
*/
function handelBeforeRemove() {
  if(props.disabled){
    ElMessage.error(props.tips)
    return false
  }
}
/**
 * 删除图片
 */
function handleRemove(removeFile) {
  const filePath = removeFile.url;
  if (filePath) {
    // 删除成功回调
    emit('update:modelValue', fileList.value);
  }
}
// 获取文件扩展名（转换为小写）
function getFileExtension(filename) {
  return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2).toLowerCase();
}
/**
 * 限制用户上传文件的格式和大小
 */
function handleBeforeUpload(file) {
  console.log("d======",file)
  let fileType = file.type.split('/')[0];
  if(props.accept && props.accept.length!=0){
    let extension = getFileExtension(file.name)
    let acceptAry = props.accept.split(',')
    if(!acceptAry.includes('.'+extension)){
      ElMessage.error(`只能上传 ${props.accept} 的文件`)
      return false
    }
  }

  if (props.videoType) {
    if (fileType != 'video') {
      ElMessage.error('只能上传视频文件');
      return false;
    }
  }
  if (props.imageType) {
    if (fileType != 'image') {
      ElMessage.error('只能上传图片文件');
      return false;
    }
  }
  if (fileType == 'image') {
    if (file.size > 20 * 1024 * 1024) {//通用
      ElMessage.error('上传图片不能大于20M');
      return false;
    }
  }else if (fileType == 'video') {
    if (file.size > 100 * 1024 * 1024) {
      ElMessage.error('上传视频不能大于100M');
      return false;
    }
  }
  return true;
}

/**
 * 预览图片
 */
const previewImg = uploadFile => {
  const { name, url } = uploadFile
  if (
    url.includes('.png') ||
    url.includes('.jpg') ||
    url.includes('.jpeg') ||
    url.includes('.gif') ||
    url.includes('.bmp') ||
    url.includes('.mp4') 
  ) {
    previewDialog.visible = true;
    previewDialog.title = name;
    previewDialog.url = url;
  } else {
    downLoadFile(name, url)
  }
};
</script>

<style>
.el-upload-list__item {
  transition: none !important;
}

.uoloadSty .el-upload--picture-card {
  /* width:110px;
    height:110px;
    line-height:110px; */
}

.disUoloadSty .el-upload{
  display: none;/* 上传按钮隐藏 */
}

/*element中file-upload组件会出现这种提示‘按delete键可删除’*/
.uploadList .el-upload-list__item.is-success:focus:not(:hover) {
  display: none !important;
}

/*上传成功后 文件名完整显示*/
.uploadList .el-upload-list__item-file-name {
  /* word-break: break-all !important;
  line-height: 16px !important;
  white-space: normal !important;
  width: 150px !important; */
}
.hideDelete .el-upload-list__item:hover .el-icon--close {
      display: none !important;
  :deep(.el-icon--close-tip){
    display: none !important; 
  }
}
.hideDelete .el-icon--close-tip {
    display: none !important; 
}
</style>