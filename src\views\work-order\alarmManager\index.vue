<!--预警管理-->
<script>
export default {
  name: 'alarmManager'
};
</script>

<script setup>
import { nextTick, onBeforeUnmount, reactive } from 'vue';
import DetailDialog from './DetailDialog.vue';
import EditDialog from './EditDialog.vue';
import CloseAlarmDialog from './CloseAlarmDialog.vue';
import { ElMessage } from 'element-plus';
import moment from 'moment';
import { authorityShow } from '@/utils/authority';

import { getAlarmPage } from '@/api/workOrder';
import { getDictChildren } from '@/api/system/dict';

const editDialogRef = ref(null);
const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  page_no: 1,
  page_size: 10,
  alarm_type:'',
  alarm_source:'',
  alarm_status:'',
  alarm_desc:'',
  alarm_time:[]
});

const DICT = {
  alarm_type:'alarm_type',//预警类型
  alarm_source:'alarm_source',//预警来源
  alarm_status:'alarm_status',//预警状态
}
const alarmStatus = {
  pending:'pending',//未处理
  processing:'processing',//处理中
  processed:'processed',//已处置
}
const alarm_typeOptions = ref([]);
const alarm_sourceOptions = ref([]);
const alarm_statusOptions = ref([]);
const dataList = ref([]);
const dialog = reactive({
  visible: false,
  id:''
});
const SOURCE = {
  view:'view',//查看
  workOrder:'workOrder',//转工单
  closed:'closed',//直接关闭
}
const editDialog = reactive({
  visible: false,
  title:'转工单',
  id:''
});

const closeDialog = reactive({
  visible: false,
  title:'关闭预警'
});

let formData = reactive({});

function getDict() {
  getDictChildren(DICT.alarm_type).then(res => {
    alarm_typeOptions.value = res
  });
  getDictChildren(DICT.alarm_source).then(res => {
    alarm_sourceOptions.value = res
  });
  getDictChildren(DICT.alarm_status).then(res => {
    alarm_statusOptions.value = res
  });
}

/**
 * 查询
 */
function handleQuery(params) {
  dataList.value = []
  loading.value = true;
  getAlarmPage({
    ...queryParams,
    ...params,
  }).then(data => {
    const { list } = data;
    dataList.value = list || [];
    total.value = data.total;
  }).finally(()=>{
    loading.value = false;
  });
}

function handleSearch() {
  console.log('queryParams',queryParams)
  let params = {
    ...queryParams,
    alarm_time: queryParams.rangeTime?.length === 2 ? [queryParams.rangeTime[0],queryParams.rangeTime[1]] : [],
    page_no: 1
  };
  delete params.rangeTime;
  delete queryParams.rangeTime;
  handleQuery(params);
}

/**
 * 重置查询
 */
function resetQuery() {
  queryParams.alarm_time = [];
  queryParams.alarm_type = '';
  queryParams.alarm_source = '';
  queryParams.alarm_status = '';
  queryParams.alarm_desc = '';
  queryParams.page_no = 1;
  queryParams.page_size = 10;
  handleQuery({ ...queryParams });
}


function openEditDialog(row,source) {
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (source == SOURCE.workOrder) {
    editDialog.id = row.id;
    editDialog.visible = true;
  } else if (source == SOURCE.view) {
    dialog.id = row.id;
    dialog.visible = true;
  }else if (source == SOURCE.closed) {
    Object.assign(formData, { ...row,source:source });
    closeDialog.visible = true;
  }
}


onMounted(() => {
  getDict();
  handleQuery({ ...queryParams });
});

const handleSizeChange = val => {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / val);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page_no > newTotalPages) {
    queryParams.page_no = newTotalPages || 1;
  }
  
  queryParams.page_size = val;
  const params = {
    ...queryParams,
    page_size: val,
    page_no: queryParams.page_no // Use the potentially adjusted page number
  };
  handleQuery({ ...params });
};

const handleCurrentChange = val => {
  queryParams.page_no = val;
  const params = {
    ...queryParams,
    page_no: val
  };
  handleQuery({ ...params });
};

onBeforeUnmount(() => {
  window.removeEventListener('setItem', () => {});
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="keyWord">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.rangeTime"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleSearch"
            />
          </el-form-item>
         
          <el-form-item label="" prop="alarm_type">
            <el-select
              class="input-serach"
              v-model="queryParams.alarm_type"
              placeholder="请选择预警类型"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in alarm_typeOptions"
                :key="index"
                :label="item.dict_name"
                :value="item.dict_value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="alarm_source">
            <el-select
              class="input-serach"
              v-model="queryParams.alarm_source"
              placeholder="请选择预警来源"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in alarm_sourceOptions"
                :key="index"
                :label="item.dict_name"
                :value="item.dict_value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="alarm_status">
            <el-select
              class="input-serach"
              v-model="queryParams.alarm_status"
              placeholder="请选择处置状态"
              clearable
               @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in alarm_statusOptions"
                :key="index"
                :label="item.dict_name"
                :value="item.dict_value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="alarm_desc">
            <el-input
              class="input-serach"
              v-model="queryParams.alarm_desc"
              placeholder="请输入预警描述"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn" style="transform: translateY(0)">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>

    <el-card shadow="never">
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540" style="margin-top: 20px;">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.page_size * (queryParams.page_no - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="预警时间" prop="alarm_time" width="200" show-overflow-tooltip />
        <el-table-column label="预警类型" prop="alarm_type_name" width="100" show-overflow-tooltip />
        <el-table-column label="预警来源" prop="alarm_source_name" width="100" show-overflow-tooltip />
        <el-table-column label="预警描述" prop="alarm_desc"  show-overflow-tooltip />
        <el-table-column label="处置状态" prop="alarm_status_name" width="100" show-overflow-tooltip/>
        <el-table-column label="处置人" prop="processor" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="处置时间" prop="process_time" width="200" show-overflow-tooltip> </el-table-column>
        <el-table-column label="处置说明" prop="process_content" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="200" v-if="authorityShow('controlAirportTask') || authorityShow('checkAirportTask') || authorityShow('deleteAirportTask')">
          <template #default="scope">
            <el-button type="primary" 
            link @click.stop="openEditDialog(scope.row,SOURCE.view)" 
            v-if="authorityShow('checkAirportTask')">详情</el-button>
            <el-button
              type="danger"
              v-if= "authorityShow('controlAirportTask') && scope.row.alarm_status === alarmStatus.pending" 
              link
              @click.stop="openEditDialog(scope.row,SOURCE.workOrder)"
              >转工单</el-button>
            <el-button
              type="danger"
              v-if="authorityShow('controlAirportTask') && scope.row.alarm_status === alarmStatus.pending"
              link
              @click.stop="openEditDialog(scope.row,SOURCE.closed)"
              >直接关闭</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-content">
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.page_no"
          v-model:page-size="queryParams.page_size"
          :background="true"
          :page-sizes="[5,10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <div v-if="editDialog.visible">
      <EditDialog
        v-if="editDialog.visible"
        ref="editDialogRef"
        v-model:visible="editDialog.visible"
        :title="editDialog.title"
        :id="editDialog.id"
        @submit="resetQuery"
      />
    </div>

    <div v-if="closeDialog.visible">
      <CloseAlarmDialog
        v-if="closeDialog.visible"
        v-model:visible="closeDialog.visible"
        :title="closeDialog.title"
        :form-data="formData"
        @submit="resetQuery"
      />
    </div>

    <div v-if="dialog.visible">
      <DetailDialog v-model:visible="dialog.visible" :id="dialog.id" />
    </div>
  </div>
</template>
<style scoped lang="scss">
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  // align-items: center;
  padding: 24px 24px 8px;
  .search-form {
    flex: 1;
  }
}
</style>
